<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://www.flowable.org/processdef"
             xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://www.omg.org/spec/BPMN/2.0/20100501/BPMN20.xsd">

  <process id="SimpleComplaintProcess" name="Simple Complaint Process" isExecutable="true">
    
    <startEvent id="startEvent" name="Complaint Submitted"/>
    
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="recordComplaint"/>
    
    <!-- Script Task to record complaint data -->
    <scriptTask id="recordComplaint" name="Record Complaint" scriptFormat="javascript">
      <script>
        <![CDATA[
        // Get variables from process
        var complaintId = execution.getVariable("complaintId") || "COMP-" + new Date().getTime();
        var businessKey = execution.getVariable("businessKey") || complaintId;
        var applicationType = execution.getVariable("applicationType") || "Complaint";
        var applicationNumber = execution.getVariable("applicationNumber") || complaintId;
        
        // Set process variables
        execution.setVariable("complaintId", complaintId);
        execution.setVariable("status", "SUBMITTED");
        execution.setVariable("state", "OPEN");
        execution.setVariable("assignedTo", "AGENT_001");
        execution.setVariable("createdDate", new Date().toISOString());
        execution.setVariable("processStep", "INITIAL_REVIEW");
        
        // Log the complaint creation
        java.lang.System.out.println("Complaint recorded: " + complaintId + " - Status: SUBMITTED");
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow2" sourceRef="recordComplaint" targetRef="agentReview"/>
    
    <!-- User Task for Agent Review -->
    <userTask id="agentReview" name="Agent Review" flowable:assignee="agent">
      <documentation>Review the complaint and take appropriate action</documentation>
      <extensionElements>
        <flowable:formProperty id="reviewAction" name="Review Action" type="enum" required="true">
          <flowable:value id="approve" name="Approve"/>
          <flowable:value id="reject" name="Reject"/>
          <flowable:value id="escalate" name="Escalate to Lead"/>
        </flowable:formProperty>
        <flowable:formProperty id="comments" name="Comments" type="string"/>
      </extensionElements>
    </userTask>
    
    <sequenceFlow id="flow3" sourceRef="agentReview" targetRef="reviewGateway"/>
    
    <!-- Gateway to decide next step based on review -->
    <exclusiveGateway id="reviewGateway" name="Review Decision"/>
    
    <!-- Approve Path -->
    <sequenceFlow id="approveFlow" sourceRef="reviewGateway" targetRef="approveComplaint">
      <conditionExpression xsi:type="tFormalExpression">${reviewAction == 'approve'}</conditionExpression>
    </sequenceFlow>
    
    <scriptTask id="approveComplaint" name="Approve Complaint" scriptFormat="javascript">
      <script>
        <![CDATA[
        execution.setVariable("status", "APPROVED");
        execution.setVariable("state", "CLOSED");
        execution.setVariable("resolvedDate", new Date().toISOString());
        execution.setVariable("processStep", "APPROVED");
        java.lang.System.out.println("Complaint approved: " + execution.getVariable("complaintId"));
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow4" sourceRef="approveComplaint" targetRef="endApproved"/>
    
    <!-- Reject Path -->
    <sequenceFlow id="rejectFlow" sourceRef="reviewGateway" targetRef="rejectComplaint">
      <conditionExpression xsi:type="tFormalExpression">${reviewAction == 'reject'}</conditionExpression>
    </sequenceFlow>
    
    <scriptTask id="rejectComplaint" name="Reject Complaint" scriptFormat="javascript">
      <script>
        <![CDATA[
        execution.setVariable("status", "REJECTED");
        execution.setVariable("state", "CLOSED");
        execution.setVariable("resolvedDate", new Date().toISOString());
        execution.setVariable("processStep", "REJECTED");
        java.lang.System.out.println("Complaint rejected: " + execution.getVariable("complaintId"));
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow5" sourceRef="rejectComplaint" targetRef="endRejected"/>
    
    <!-- Escalate Path -->
    <sequenceFlow id="escalateFlow" sourceRef="reviewGateway" targetRef="escalateToLead">
      <conditionExpression xsi:type="tFormalExpression">${reviewAction == 'escalate'}</conditionExpression>
    </sequenceFlow>
    
    <userTask id="escalateToLead" name="Lead Review" flowable:assignee="lead">
      <documentation>Lead review of escalated complaint</documentation>
      <extensionElements>
        <flowable:formProperty id="leadAction" name="Lead Action" type="enum" required="true">
          <flowable:value id="approve" name="Approve"/>
          <flowable:value id="reject" name="Reject"/>
        </flowable:formProperty>
        <flowable:formProperty id="leadComments" name="Lead Comments" type="string"/>
      </extensionElements>
    </userTask>
    
    <sequenceFlow id="flow6" sourceRef="escalateToLead" targetRef="leadGateway"/>
    
    <exclusiveGateway id="leadGateway" name="Lead Decision"/>
    
    <sequenceFlow id="leadApproveFlow" sourceRef="leadGateway" targetRef="approveComplaint">
      <conditionExpression xsi:type="tFormalExpression">${leadAction == 'approve'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="leadRejectFlow" sourceRef="leadGateway" targetRef="rejectComplaint">
      <conditionExpression xsi:type="tFormalExpression">${leadAction == 'reject'}</conditionExpression>
    </sequenceFlow>
    
    <!-- End Events -->
    <endEvent id="endApproved" name="Complaint Approved"/>
    <endEvent id="endRejected" name="Complaint Rejected"/>
    
  </process>

</definitions>
