import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface ProcessInstance {
  id: string;
  processDefinitionId: string;
  processDefinitionKey: string;
  businessKey?: string;
  suspended: boolean;
  ended: boolean;
  startTime: string;
  startUserId?: string;
  variables?: any;
}

interface UpdateProcessRequest {
  action: 'suspend' | 'activate';
}

const ProcessInstanceManager: React.FC = () => {
  const [processInstances, setProcessInstances] = useState<ProcessInstance[]>([]);
  const [selectedInstance, setSelectedInstance] = useState<ProcessInstance | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Flowable REST API configuration
  const FLOWABLE_BASE_URL = 'http://localhost:8080/flowable-rest';
  const AUTH_HEADER = {
    Authorization: 'Basic ' + btoa('rest-admin:test') // Default Flowable credentials
  };

  // Fetch all process instances
  const fetchProcessInstances = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${FLOWABLE_BASE_URL}/service/runtime/process-instances`, {
        headers: AUTH_HEADER
      });
      setProcessInstances(response.data.data || []);
    } catch (err: any) {
      setError(`Failed to fetch process instances: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Update process instance (suspend/activate)
  const updateProcessInstance = async (instanceId: string, action: 'suspend' | 'activate') => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const updateData: UpdateProcessRequest = { action };
      
      await axios.put(
        `${FLOWABLE_BASE_URL}/service/runtime/process-instances/${instanceId}`,
        updateData,
        { headers: AUTH_HEADER }
      );
      
      setSuccess(`Process instance ${action}d successfully!`);
      await fetchProcessInstances(); // Refresh the list
    } catch (err: any) {
      setError(`Failed to ${action} process instance: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Delete process instance
  const deleteProcessInstance = async (instanceId: string, reason: string = 'Deleted via UI') => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      await axios.delete(
        `${FLOWABLE_BASE_URL}/service/runtime/process-instances/${instanceId}?deleteReason=${encodeURIComponent(reason)}`,
        { headers: AUTH_HEADER }
      );
      
      setSuccess('Process instance deleted successfully!');
      await fetchProcessInstances(); // Refresh the list
    } catch (err: any) {
      setError(`Failed to delete process instance: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Get process instance variables
  const fetchProcessVariables = async (instanceId: string) => {
    try {
      const response = await axios.get(
        `${FLOWABLE_BASE_URL}/service/runtime/process-instances/${instanceId}/variables`,
        { headers: AUTH_HEADER }
      );
      return response.data;
    } catch (err: any) {
      setError(`Failed to fetch variables: ${err.message}`);
      return [];
    }
  };

  useEffect(() => {
    fetchProcessInstances();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Process Instance Manager</h1>
        <p className="text-gray-600">Manage Flowable process instances - suspend, activate, or delete</p>
      </div>

      {/* Action Buttons */}
      <div className="mb-6 flex gap-4">
        <button
          onClick={fetchProcessInstances}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg font-medium"
        >
          {loading ? 'Loading...' : 'Refresh List'}
        </button>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
          {success}
        </div>
      )}

      {/* Process Instances Table */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 bg-gray-50 border-b">
          <h2 className="text-xl font-semibold text-gray-800">Process Instances ({processInstances.length})</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Instance ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Process Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Business Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Start Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {processInstances.map((instance) => (
                <tr key={instance.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    {instance.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {instance.processDefinitionKey}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {instance.businessKey || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      instance.ended 
                        ? 'bg-gray-100 text-gray-800' 
                        : instance.suspended 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {instance.ended ? 'Ended' : instance.suspended ? 'Suspended' : 'Active'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(instance.startTime)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    {!instance.ended && (
                      <>
                        {instance.suspended ? (
                          <button
                            onClick={() => updateProcessInstance(instance.id, 'activate')}
                            disabled={loading}
                            className="text-green-600 hover:text-green-900 disabled:text-green-300"
                          >
                            Activate
                          </button>
                        ) : (
                          <button
                            onClick={() => updateProcessInstance(instance.id, 'suspend')}
                            disabled={loading}
                            className="text-yellow-600 hover:text-yellow-900 disabled:text-yellow-300"
                          >
                            Suspend
                          </button>
                        )}
                        <button
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this process instance?')) {
                              deleteProcessInstance(instance.id);
                            }
                          }}
                          disabled={loading}
                          className="text-red-600 hover:text-red-900 disabled:text-red-300"
                        >
                          Delete
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => setSelectedInstance(instance)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {processInstances.length === 0 && !loading && (
          <div className="text-center py-8 text-gray-500">
            No process instances found
          </div>
        )}
      </div>

      {/* Process Instance Details Modal */}
      {selectedInstance && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-96 overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Process Instance Details</h3>
                <button
                  onClick={() => setSelectedInstance(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-3">
                <div><strong>ID:</strong> {selectedInstance.id}</div>
                <div><strong>Process Definition ID:</strong> {selectedInstance.processDefinitionId}</div>
                <div><strong>Process Key:</strong> {selectedInstance.processDefinitionKey}</div>
                <div><strong>Business Key:</strong> {selectedInstance.businessKey || 'N/A'}</div>
                <div><strong>Status:</strong> {selectedInstance.ended ? 'Ended' : selectedInstance.suspended ? 'Suspended' : 'Active'}</div>
                <div><strong>Start Time:</strong> {formatDate(selectedInstance.startTime)}</div>
                <div><strong>Start User:</strong> {selectedInstance.startUserId || 'N/A'}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessInstanceManager;
