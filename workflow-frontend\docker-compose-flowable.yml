version: '3.8'

services:
  flowable-rest:
    image: flowable/flowable-rest:latest
    container_name: flowable-rest-db
    ports:
      - "8080:8080"
    environment:
      # Database Configuration - Connect to your PostgreSQL
      - SPRING_DATASOURCE_URL=****************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=root
      - SPRING_DATASOURCE_DRIVER_CLASS_NAME=org.postgresql.Driver
      
      # Flowable Configuration
      - FLOWABLE_DATABASE_SCHEMA_UPDATE=true
      - FLOWABLE_ASYNC_EXECUTOR_ACTIVATE=true
      - FLOWABLE_DATABASE_SCHEMA=public
      
      # REST API Configuration
      - FLOWABLE_REST_API_ENABLED=true
      - FLOWABLE_REST_APP_AUTHENTICATION_MODE=any-user
      
      # Admin User Configuration
      - FLOWABLE_ADMIN_USERID=rest-admin
      - FLOWABLE_ADMIN_PASSWORD=test
      - FLOWABLE_ADMIN_FIRSTNAME=Rest
      - FLOWABLE_ADMIN_LASTNAME=Admin
      
      # JPA Configuration
      - SPRING_JPA_HIBERNATE_DDL_AUTO=update
      - SPRING_JPA_SHOW_SQL=true
      - SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT=org.hibernate.dialect.PostgreSQLDialect
      
    volumes:
      # Mount a directory for process definitions
      - ./processes:/opt/flowable/processes
    restart: unless-stopped
    depends_on:
      - postgres
    networks:
      - flowable-network

  # Optional: Include PostgreSQL if you want it in the same network
  postgres:
    image: postgres:13
    container_name: flowable-postgres
    environment:
      - POSTGRES_DB=workflow
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=root
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - flowable-network

networks:
  flowable-network:
    driver: bridge

volumes:
  postgres_data:
