<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flowable Process Instance Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100">
    <div id="app" class="max-w-7xl mx-auto p-6">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Flowable Process Instance Manager</h1>
            <p class="text-gray-600">Manage your Flowable process instances - view, suspend, activate, or delete</p>
        </div>

        <!-- Status Messages -->
        <div id="messages" class="mb-4"></div>

        <!-- Action Buttons -->
        <div class="mb-6 flex gap-4">
            <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium">
                Refresh List
            </button>
            <button id="startProcessBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium">
                Start New Process
            </button>
        </div>

        <!-- Process Instances Table -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-gray-50 border-b">
                <h2 class="text-xl font-semibold text-gray-800">Process Instances (<span id="instanceCount">0</span>)</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Instance ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process Key</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Key</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="processTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Process instances will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <div id="noInstances" class="text-center py-8 text-gray-500 hidden">
                No process instances found
            </div>
        </div>

        <!-- Start Process Modal -->
        <div id="startProcessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Start New Process</h3>
                        <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600">✕</button>
                    </div>
                    
                    <form id="startProcessForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Process Definition Key</label>
                            <select id="processKey" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                <option value="Hrdc_workflow">HRDC Workflow</option>
                                <option value="Complaint_Workflow">Complaint Workflow</option>
                                <option value="Appeal_Workflow">Appeal Workflow</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Business Key (Optional)</label>
                            <input type="text" id="businessKey" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter business key">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Application Number</label>
                            <input type="text" id="applicationNumber" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter application number">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Application Type</label>
                            <select id="applicationType" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                <option value="preapproval">Pre-approval</option>
                                <option value="funding">Funding</option>
                                <option value="complaint">Complaint</option>
                                <option value="appeal">Appeal</option>
                            </select>
                        </div>
                        
                        <div class="flex gap-3 pt-4">
                            <button type="submit" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg font-medium">
                                Start Process
                            </button>
                            <button type="button" id="cancelBtn" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg font-medium">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration - Using proxy to avoid CORS issues
        const FLOWABLE_BASE_URL = '/api/flowable';  // Proxy endpoint
        const AUTH_HEADER = '';  // Auth handled by proxy

        // DOM elements
        const refreshBtn = document.getElementById('refreshBtn');
        const startProcessBtn = document.getElementById('startProcessBtn');
        const processTableBody = document.getElementById('processTableBody');
        const instanceCount = document.getElementById('instanceCount');
        const noInstances = document.getElementById('noInstances');
        const messages = document.getElementById('messages');
        const startProcessModal = document.getElementById('startProcessModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const startProcessForm = document.getElementById('startProcessForm');

        // Utility functions
        function showMessage(message, type = 'info') {
            const alertClass = type === 'error' ? 'bg-red-100 border-red-400 text-red-700' : 
                              type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 
                              'bg-blue-100 border-blue-400 text-blue-700';
            
            messages.innerHTML = `<div class="p-4 ${alertClass} border rounded-lg">${message}</div>`;
            setTimeout(() => messages.innerHTML = '', 5000);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        }

        function getStatusBadge(instance) {
            if (instance.ended) {
                return '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Ended</span>';
            } else if (instance.suspended) {
                return '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Suspended</span>';
            } else {
                return '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>';
            }
        }

        // API functions
        async function fetchProcessInstances() {
            try {
                refreshBtn.textContent = 'Loading...';
                refreshBtn.disabled = true;

                const response = await axios.get(`${FLOWABLE_BASE_URL}/service/runtime/process-instances`);

                const instances = response.data.data || [];
                displayProcessInstances(instances);
                showMessage(`Loaded ${instances.length} process instances`, 'success');
            } catch (error) {
                showMessage(`Failed to fetch process instances: ${error.message}`, 'error');
                console.error('Error:', error);
            } finally {
                refreshBtn.textContent = 'Refresh List';
                refreshBtn.disabled = false;
            }
        }

        async function fetchProcessDefinitions() {
            try {
                const response = await axios.get(`${FLOWABLE_BASE_URL}/service/repository/process-definitions`);
                const definitions = response.data.data || [];

                const select = document.getElementById('processDefinitionKey');
                select.innerHTML = '<option value="">Select a process definition...</option>';

                if (definitions.length === 0) {
                    select.innerHTML = '<option value="">No process definitions available</option>';
                    showMessage('No process definitions found. You need to deploy a BPMN process first.', 'warning');
                } else {
                    definitions.forEach(def => {
                        const option = document.createElement('option');
                        option.value = def.key;

                        // Mark known working processes
                        const workingProcesses = [
                            'oneTaskProcess',
                            'fixSystemFailure',
                            'escalationExample',
                            'SimpleComplaintProcess',
                            'DatabaseComplaintProcess',
                            'simpleProcess'
                        ];
                        const databaseProcesses = ['DatabaseComplaintProcess'];
                        const isWorking = workingProcesses.includes(def.key);
                        const isDatabase = databaseProcesses.includes(def.key);

                        let status = ' ⚠️';
                        if (isDatabase) {
                            status = ' 🗄️';
                        } else if (isWorking) {
                            status = ' ✅';
                        }

                        option.textContent = `${def.name || def.key} (v${def.version})${status}`;
                        select.appendChild(option);
                    });

                    // Add a note about the status indicators
                    const noteOption = document.createElement('option');
                    noteOption.disabled = true;
                    noteOption.textContent = '--- ✅ = Working, 🗄️ = Database Connected, ⚠️ = May Need Dependencies ---';
                    select.appendChild(noteOption);
                }

                return definitions;
            } catch (error) {
                console.error('Error fetching process definitions:', error);
                showMessage(`Failed to fetch process definitions: ${error.message}`, 'error');
                return [];
            }
        }

        async function updateProcessInstance(instanceId, action) {
            try {
                await axios.put(
                    `${FLOWABLE_BASE_URL}/service/runtime/process-instances/${instanceId}`,
                    { action }
                );
                
                showMessage(`Process instance ${action}d successfully!`, 'success');
                await fetchProcessInstances();
            } catch (error) {
                showMessage(`Failed to ${action} process instance: ${error.message}`, 'error');
            }
        }

        async function deleteProcessInstance(instanceId) {
            if (!confirm('Are you sure you want to delete this process instance?')) return;
            
            try {
                await axios.delete(
                    `${FLOWABLE_BASE_URL}/service/runtime/process-instances/${instanceId}?deleteReason=Deleted via UI`
                );
                
                showMessage('Process instance deleted successfully!', 'success');
                await fetchProcessInstances();
            } catch (error) {
                showMessage(`Failed to delete process instance: ${error.message}`, 'error');
            }
        }

        async function startProcess(processKey, businessKey, variables) {
            try {
                const payload = {
                    processDefinitionKey: processKey
                };

                // Only add businessKey if it has a value
                if (businessKey && businessKey.trim()) {
                    payload.businessKey = businessKey.trim();
                }

                // Only add variables if they exist and are not empty
                if (variables && variables.length > 0) {
                    payload.variables = variables;
                }

                console.log('Starting process with payload:', JSON.stringify(payload, null, 2));

                const response = await axios.post(
                    `${FLOWABLE_BASE_URL}/service/runtime/process-instances`,
                    payload
                );

                showMessage(`Process started successfully! Instance ID: ${response.data.id}`, 'success');
                await fetchProcessInstances();
                startProcessModal.classList.add('hidden');
            } catch (error) {
                console.error('Error starting process:', error);
                console.error('Error response:', error.response);

                let errorMessage = 'Internal server error';
                if (error.response && error.response.data) {
                    if (error.response.data.message) {
                        errorMessage = error.response.data.message;
                    } else if (error.response.data.exception) {
                        errorMessage = error.response.data.exception;
                    } else if (typeof error.response.data === 'string') {
                        errorMessage = error.response.data;
                    }
                }

                showMessage(`Failed to start process: ${errorMessage}`, 'error');
                console.log('Full error details:', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data
                });
            }
        }

        // Display functions
        function displayProcessInstances(instances) {
            instanceCount.textContent = instances.length;
            
            if (instances.length === 0) {
                processTableBody.innerHTML = '';
                noInstances.classList.remove('hidden');
                return;
            }
            
            noInstances.classList.add('hidden');
            
            processTableBody.innerHTML = instances.map(instance => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">${instance.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${instance.processDefinitionKey}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${instance.businessKey || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${getStatusBadge(instance)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDate(instance.startTime)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        ${!instance.ended ? `
                            ${instance.suspended ? 
                                `<button onclick="updateProcessInstance('${instance.id}', 'activate')" class="text-green-600 hover:text-green-900">Activate</button>` :
                                `<button onclick="updateProcessInstance('${instance.id}', 'suspend')" class="text-yellow-600 hover:text-yellow-900">Suspend</button>`
                            }
                            <button onclick="deleteProcessInstance('${instance.id}')" class="text-red-600 hover:text-red-900">Delete</button>
                        ` : ''}
                    </td>
                </tr>
            `).join('');
        }

        // Event listeners
        refreshBtn.addEventListener('click', fetchProcessInstances);
        startProcessBtn.addEventListener('click', () => {
            startProcessModal.classList.remove('hidden');
            fetchProcessDefinitions();
        });
        closeModalBtn.addEventListener('click', () => startProcessModal.classList.add('hidden'));
        cancelBtn.addEventListener('click', () => startProcessModal.classList.add('hidden'));

        startProcessForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const processKey = document.getElementById('processKey').value;
            const businessKey = document.getElementById('businessKey').value;
            const applicationNumber = document.getElementById('applicationNumber').value;
            const applicationType = document.getElementById('applicationType').value;
            
            const variables = [];
            if (applicationNumber) {
                variables.push({ name: 'applicationNumber', value: applicationNumber });
            }
            if (applicationType) {
                variables.push({ name: 'applicationType', value: applicationType });
            }
            variables.push({ name: 'role', value: 'Agent_lead' });
            
            startProcess(processKey, businessKey, variables);
        });

        // Initialize
        fetchProcessInstances();
    </script>
</body>
</html>
