const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function deployNewProcesses() {
    const processes = [
        {
            file: 'simple-complaint-process.bpmn20.xml',
            name: 'Simple Complaint Process'
        },
        {
            file: 'database-complaint-process.bpmn20.xml',
            name: 'Database Complaint Process'
        }
    ];

    for (const process of processes) {
        try {
            console.log(`\n📦 Deploying ${process.name}...`);
            
            const form = new FormData();
            
            // Add the BPMN file
            const bpmnPath = path.join(__dirname, 'processes', process.file);
            
            if (!fs.existsSync(bpmnPath)) {
                console.log(`❌ File not found: ${bpmnPath}`);
                continue;
            }
            
            form.append('deployment', fs.createReadStream(bpmnPath), {
                filename: process.file,
                contentType: 'application/xml'
            });
            
            // Add deployment name
            form.append('deploymentName', process.name);
            
            const response = await axios.post(
                'http://localhost:8080/flowable-rest/service/repository/deployments',
                form,
                {
                    headers: {
                        ...form.getHeaders(),
                        'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64')
                    }
                }
            );
            
            console.log(`✅ ${process.name} deployed successfully!`);
            console.log(`   Deployment ID: ${response.data.id}`);
            console.log(`   Deployment Time: ${response.data.deploymentTime}`);
            
        } catch (error) {
            console.error(`❌ Error deploying ${process.name}:`, error.message);
            if (error.response) {
                console.error('   Response data:', error.response.data);
            }
        }
    }
    
    // List all process definitions to confirm
    try {
        console.log('\n📋 All Available Process Definitions:');
        const defsResponse = await axios.get(
            'http://localhost:8080/flowable-rest/service/repository/process-definitions',
            {
                headers: {
                    'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64')
                }
            }
        );
        
        defsResponse.data.data.forEach(def => {
            const workingProcesses = [
                'oneTaskProcess', 
                'SimpleComplaintProcess',
                'DatabaseComplaintProcess',
                'simpleProcess'
            ];
            const status = workingProcesses.includes(def.key) ? '✅' : '⚠️';
            console.log(`${status} ${def.name} (${def.key}) v${def.version}`);
        });
        
        console.log('\n🎯 Recommended processes to test:');
        console.log('✅ SimpleComplaintProcess - Works without database');
        console.log('🗄️ DatabaseComplaintProcess - Connects to your database');
        console.log('✅ oneTaskProcess - Simple test process');
        
    } catch (error) {
        console.error('❌ Error listing process definitions:', error.message);
    }
}

deployNewProcesses();
