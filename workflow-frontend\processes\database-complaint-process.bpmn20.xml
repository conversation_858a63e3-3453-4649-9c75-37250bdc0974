<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://www.flowable.org/processdef"
             xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://www.omg.org/spec/BPMN/2.0/20100501/BPMN20.xsd">

  <process id="DatabaseComplaintProcess" name="Database Complaint Process" isExecutable="true">
    
    <startEvent id="startEvent" name="Start"/>
    
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="fetchComplaintData"/>
    
    <!-- Script Task to simulate database fetch -->
    <scriptTask id="fetchComplaintData" name="Fetch Complaint Data" scriptFormat="javascript">
      <script>
        <![CDATA[
        // Simulate database fetch - in real implementation this would call your REST API
        var complaintId = execution.getVariable("complaintId");
        var businessKey = execution.getVariable("businessKey") || complaintId;

        // Simulate API call to your complaint service
        // In real implementation: var response = restTemplate.getForObject("http://localhost:3460/api/complaints/" + complaintId);

        // Set individual variables instead of complex object
        execution.setVariable("referenceNumber", businessKey);
        execution.setVariable("currentStatus", "SUBMITTED");
        execution.setVariable("currentState", "OPEN");
        execution.setVariable("assignee", "AGENT_001");
        execution.setVariable("createdAt", new Date().toISOString());
        execution.setVariable("updatedAt", new Date().toISOString());
        execution.setVariable("dataFetched", true);

        java.lang.System.out.println("Simulated database fetch for complaint: " + complaintId);
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow2" sourceRef="fetchComplaintData" targetRef="processComplaintData"/>
    
    <!-- Script Task to process the fetched data -->
    <scriptTask id="processComplaintData" name="Process Complaint Data" scriptFormat="javascript">
      <script>
        <![CDATA[
        // Check if data was fetched successfully
        var dataFetched = execution.getVariable("dataFetched");
        var complaintId = execution.getVariable("complaintId");

        if (dataFetched) {
          java.lang.System.out.println("Complaint data processed for: " + complaintId);
        } else {
          // No data found - mark for creation
          execution.setVariable("dataFetched", false);
          execution.setVariable("currentStatus", "NEW");
          execution.setVariable("currentState", "SUBMITTED");

          java.lang.System.out.println("No complaint data found for ID: " + complaintId);
        }
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow3" sourceRef="processComplaintData" targetRef="dataGateway"/>
    
    <!-- Gateway to check if data was found -->
    <exclusiveGateway id="dataGateway" name="Data Found?"/>
    
    <!-- Path for existing complaint -->
    <sequenceFlow id="existingFlow" sourceRef="dataGateway" targetRef="updateComplaintStatus">
      <conditionExpression xsi:type="tFormalExpression">${dataFetched == true}</conditionExpression>
    </sequenceFlow>
    
    <!-- Path for new complaint -->
    <sequenceFlow id="newFlow" sourceRef="dataGateway" targetRef="createComplaintRecord">
      <conditionExpression xsi:type="tFormalExpression">${dataFetched == false}</conditionExpression>
    </sequenceFlow>
    
    <!-- Script Task to simulate creating complaint record -->
    <scriptTask id="createComplaintRecord" name="Create Complaint Record" scriptFormat="javascript">
      <script>
        <![CDATA[
        var complaintId = execution.getVariable("complaintId");
        var businessKey = execution.getVariable("businessKey");

        // Simulate API call to create complaint
        // In real implementation: restTemplate.postForObject("http://localhost:3460/api/complaints", complaintData);

        execution.setVariable("status", "SUBMITTED");
        execution.setVariable("state", "OPEN");
        execution.setVariable("assignee", "AGENT_001");
        execution.setVariable("createdAt", new Date().toISOString());

        java.lang.System.out.println("Simulated complaint creation: " + complaintId);
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow4" sourceRef="createComplaintRecord" targetRef="agentReview"/>
    
    <!-- Script Task to simulate updating complaint -->
    <scriptTask id="updateComplaintStatus" name="Update Complaint Status" scriptFormat="javascript">
      <script>
        <![CDATA[
        var complaintId = execution.getVariable("complaintId");

        // Simulate API call to update complaint
        // In real implementation: restTemplate.put("http://localhost:3460/api/complaints/" + complaintId, updateData);

        execution.setVariable("status", "IN_PROGRESS");
        execution.setVariable("state", "UNDER_REVIEW");
        execution.setVariable("updatedAt", new Date().toISOString());

        java.lang.System.out.println("Simulated complaint update: " + complaintId + " -> IN_PROGRESS");
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow5" sourceRef="updateComplaintStatus" targetRef="agentReview"/>
    
    <!-- User Task for Agent Review -->
    <userTask id="agentReview" name="Agent Review" flowable:assignee="agent">
      <documentation>Review the complaint and take action</documentation>
      <extensionElements>
        <flowable:formProperty id="action" name="Action" type="enum" required="true">
          <flowable:value id="resolve" name="Resolve"/>
          <flowable:value id="escalate" name="Escalate"/>
          <flowable:value id="reject" name="Reject"/>
        </flowable:formProperty>
        <flowable:formProperty id="comments" name="Comments" type="string"/>
      </extensionElements>
    </userTask>
    
    <sequenceFlow id="flow6" sourceRef="agentReview" targetRef="finalUpdate"/>
    
    <!-- Script Task to simulate final update -->
    <scriptTask id="finalUpdate" name="Update Final Status" scriptFormat="javascript">
      <script>
        <![CDATA[
        var complaintId = execution.getVariable("complaintId");
        var action = execution.getVariable("action");

        // Simulate API call to update final status
        // In real implementation: restTemplate.put("http://localhost:3460/api/complaints/" + complaintId + "/status", finalData);

        var finalStatus, finalState;

        if (action === 'resolve') {
          finalStatus = 'RESOLVED';
          finalState = 'CLOSED';
        } else if (action === 'reject') {
          finalStatus = 'REJECTED';
          finalState = 'CLOSED';
        } else if (action === 'escalate') {
          finalStatus = 'ESCALATED';
          finalState = 'ESCALATED';
        } else {
          finalStatus = 'COMPLETED';
          finalState = 'CLOSED';
        }

        execution.setVariable("finalStatus", finalStatus);
        execution.setVariable("finalState", finalState);
        execution.setVariable("completedAt", new Date().toISOString());

        java.lang.System.out.println("Simulated final update: " + complaintId + " -> " + finalStatus);
        ]]>
      </script>
    </scriptTask>
    
    <sequenceFlow id="flow7" sourceRef="finalUpdate" targetRef="endEvent"/>
    
    <endEvent id="endEvent" name="End"/>
    
  </process>

</definitions>
