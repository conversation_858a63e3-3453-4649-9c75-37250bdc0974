const axios = require('axios');

async function testWorkflowIntegration() {
    try {
        console.log('🔍 Testing Workflow Integration Service...');
        
        // Test 1: Check if workflow service is running
        console.log('\n1. Testing workflow service health...');
        try {
            const healthResponse = await axios.get('http://localhost:8082/actuator/health');
            console.log('✅ Workflow service is running:', healthResponse.data);
        } catch (error) {
            console.log('❌ Workflow service health check failed:', error.message);
        }
        
        // Test 2: Check Flowable connection from workflow service
        console.log('\n2. Testing Flowable connection from workflow service...');
        try {
            const flowableResponse = await axios.get('http://localhost:8082/api/workflow/process-definitions');
            console.log('✅ Workflow service can connect to Flowable');
            console.log('Available processes:', flowableResponse.data);
        } catch (error) {
            console.log('❌ Workflow service cannot connect to Flowable:', error.message);
        }
        
        // Test 3: Try starting complaint workflow through the workflow service
        console.log('\n3. Testing complaint workflow start through workflow service...');
        try {
            const startResponse = await axios.post('http://localhost:8082/api/workflow/complaints/start', {
                complaintId: 'TEST-COMPLAINT-123'
            });
            console.log('✅ Complaint workflow started successfully:', startResponse.data);
        } catch (error) {
            console.log('❌ Failed to start complaint workflow through service:', error.message);
            if (error.response) {
                console.log('Response data:', error.response.data);
            }
        }
        
        // Test 4: Check if the delegates are registered as Spring beans
        console.log('\n4. Testing if Spring beans are available...');
        try {
            const beansResponse = await axios.get('http://localhost:8082/actuator/beans');
            const beans = beansResponse.data.contexts.application.beans;
            
            const requiredBeans = [
                'fetchComplaintDataDelegate',
                'notifyComplaintDelegate'
            ];
            
            requiredBeans.forEach(beanName => {
                if (beans[beanName]) {
                    console.log(`✅ Bean '${beanName}' is registered`);
                } else {
                    console.log(`❌ Bean '${beanName}' is NOT registered`);
                }
            });
        } catch (error) {
            console.log('❌ Could not check Spring beans:', error.message);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testWorkflowIntegration();
