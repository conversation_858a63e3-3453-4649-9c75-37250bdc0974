<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://www.flowable.org/processdef"
             xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://www.omg.org/spec/BPMN/2.0/20100501/BPMN20.xsd">

  <process id="simpleProcess" name="Simple Test Process" isExecutable="true">
    
    <startEvent id="startEvent" name="Start"/>
    
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="userTask"/>
    
    <userTask id="userTask" name="Review Task" flowable:assignee="admin">
      <documentation>This is a simple user task for testing</documentation>
    </userTask>
    
    <sequenceFlow id="flow2" sourceRef="userTask" targetRef="endEvent"/>
    
    <endEvent id="endEvent" name="End"/>
    
  </process>

</definitions>
