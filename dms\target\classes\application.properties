spring.application.name=DMS
# Default profile
spring.profiles.active=dev
#
# App Config
app.build.name=hrdc
app.build.version=0.0.1-SNAPSHOT
app.build.date=@build.timestamp@
#
 # PostgreSQL Configuration
# spring.datasource.url=jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:dms}
# spring.datasource.username=${DB_USERNAME:postgres}
# spring.datasource.password=${DB_PASSWORD:administrator}
# spring.jpa.hibernate.ddl-auto=update
# spring.liquibase.enabled=${SPRING_LIQUIBASE_ENABLED:true}

# Paperless-ngx API URL and Key
# paperless.api.url=http://localhost:8000
# paperless.api.token=1bfca2f81fb352f91a55ed3eac759e3ca46ab777
# paperless.api.auth.username=admin
# paperless.api.auth.password=admin

paperless.api.url=http://localhost:8000
paperless.api.token=1bfca2f81fb352f91a55ed3eac759e3ca46ab777
paperless.api.auth.username=admin
paperless.api.auth.password=admin
# Increase the delay between polling attempts for better reliability
paperless.upload.delay.seconds=5
# Maximum number of polling attempts
paperless.upload.max.attempts=60

