const axios = require('axios');

async function testNewProcesses() {
    const baseURL = 'http://localhost:8080/flowable-rest';
    const auth = 'Basic ' + Buffer.from('rest-admin:test').toString('base64');
    
    console.log('🧪 Testing New Processes...\n');
    
    // Test 1: SimpleComplaintProcess
    try {
        console.log('1️⃣ Testing SimpleComplaintProcess...');
        
        const payload1 = {
            processDefinitionKey: "SimpleComplaintProcess",
            businessKey: "SIMPLE-COMP-" + Date.now(),
            variables: [
                {
                    name: "complaintId",
                    value: "COMP-" + Date.now()
                },
                {
                    name: "applicationType",
                    value: "Complaint"
                }
            ]
        };
        
        const response1 = await axios.post(
            `${baseURL}/service/runtime/process-instances`,
            payload1,
            { headers: { 'Authorization': auth, 'Content-Type': 'application/json' } }
        );
        
        console.log('✅ SimpleComplaintProcess started successfully!');
        console.log(`   Process Instance ID: ${response1.data.id}`);
        console.log(`   Business Key: ${response1.data.businessKey}`);
        
    } catch (error) {
        console.log('❌ SimpleComplaintProcess failed:', error.message);
        if (error.response) {
            console.log('   Error details:', error.response.data);
        }
    }
    
    // Test 2: DatabaseComplaintProcess
    try {
        console.log('\n2️⃣ Testing DatabaseComplaintProcess...');
        
        const payload2 = {
            processDefinitionKey: "DatabaseComplaintProcess",
            businessKey: "DB-COMP-" + Date.now(),
            variables: [
                {
                    name: "complaintId",
                    value: "DB-COMP-" + Date.now()
                },
                {
                    name: "applicationType",
                    value: "Database Complaint"
                }
            ]
        };
        
        const response2 = await axios.post(
            `${baseURL}/service/runtime/process-instances`,
            payload2,
            { headers: { 'Authorization': auth, 'Content-Type': 'application/json' } }
        );
        
        console.log('✅ DatabaseComplaintProcess started successfully!');
        console.log(`   Process Instance ID: ${response2.data.id}`);
        console.log(`   Business Key: ${response2.data.businessKey}`);
        
    } catch (error) {
        console.log('❌ DatabaseComplaintProcess failed:', error.message);
        if (error.response) {
            console.log('   Error details:', error.response.data);
        }
    }
    
    // Test 3: List current process instances
    try {
        console.log('\n3️⃣ Current Process Instances:');
        
        const instancesResponse = await axios.get(
            `${baseURL}/service/runtime/process-instances`,
            { headers: { 'Authorization': auth } }
        );
        
        const instances = instancesResponse.data.data || [];
        console.log(`Found ${instances.length} running process instances:`);
        
        instances.forEach((instance, index) => {
            const status = instance.ended ? '🔴 ENDED' : '🟢 RUNNING';
            console.log(`   ${index + 1}. ${status} ${instance.processDefinitionName}`);
            console.log(`      ID: ${instance.id}`);
            console.log(`      Business Key: ${instance.businessKey || 'N/A'}`);
            console.log(`      Started: ${instance.startTime}`);
        });
        
    } catch (error) {
        console.log('❌ Failed to get process instances:', error.message);
    }
    
    // Test 4: List available tasks
    try {
        console.log('\n4️⃣ Available Tasks:');
        
        const tasksResponse = await axios.get(
            `${baseURL}/service/runtime/tasks`,
            { headers: { 'Authorization': auth } }
        );
        
        const tasks = tasksResponse.data.data || [];
        console.log(`Found ${tasks.length} available tasks:`);
        
        tasks.forEach((task, index) => {
            console.log(`   ${index + 1}. 📋 ${task.name}`);
            console.log(`      Task ID: ${task.id}`);
            console.log(`      Process: ${task.processDefinitionName}`);
            console.log(`      Assignee: ${task.assignee || 'Unassigned'}`);
            console.log(`      Created: ${task.createTime}`);
        });
        
        if (tasks.length > 0) {
            console.log('\n💡 You can complete these tasks through the Flowable UI or REST API');
        }
        
    } catch (error) {
        console.log('❌ Failed to get tasks:', error.message);
    }
    
    console.log('\n🎯 Summary:');
    console.log('✅ Your new processes are working!');
    console.log('🌐 Test them in your frontend at: http://localhost:3002');
    console.log('📋 Look for processes marked with ✅ in the dropdown');
}

testNewProcesses();
