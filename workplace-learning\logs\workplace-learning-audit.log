2025-07-04T10:34:33.010+05:30  INFO 17880 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 17880 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-04T10:34:33.024+05:30  INFO 17880 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-04T10:34:33.113+05:30  INFO 17880 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04T10:34:33.114+05:30  INFO 17880 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04T10:34:34.919+05:30  INFO 17880 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-04T10:34:35.239+05:30  INFO 17880 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 310 ms. Found 31 JPA repository interfaces.
2025-07-04T10:34:35.614+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-07-04T10:34:36.089+05:30  WARN 17880 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-04T10:34:36.094+05:30  WARN 17880 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-04T10:34:36.101+05:30  WARN 17880 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-04T10:34:36.105+05:30  WARN 17880 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-04T10:34:36.108+05:30  WARN 17880 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-04T10:34:36.628+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-04T10:34:36.644+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-04T10:34:36.645+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-04T10:34:36.717+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-04T10:34:36.718+05:30  INFO 17880 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3603 ms
2025-07-04T10:34:36.905+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-04T10:34:37.153+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@5fbc7a34
2025-07-04T10:34:37.157+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-04T10:34:37.176+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-04T10:34:37.763+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-04T10:34:37.858+05:30  INFO 17880 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-04T10:34:37.914+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-04T10:34:38.473+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-04T10:34:42.203+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-04T10:34:43.464+05:30  INFO 17880 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-04T10:34:44.082+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-04T10:34:47.549+05:30  WARN 17880 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-04T10:34:47.765+05:30  WARN 17880 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-04T10:34:47.911+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-04T10:34:48.129+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-04T10:34:53.424+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-04T10:34:54.933+05:30  INFO 17880 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-04T10:34:55.053+05:30  WARN 17880 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-04T10:34:55.229+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-04T10:34:55.272+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-04T10:34:55.281+05:30  INFO 17880 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T10:34:55.300+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-04T10:34:55.300+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-04T10:34:55.301+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-04T10:34:55.301+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-04T10:34:55.302+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-04T10:34:55.302+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-04T10:34:55.303+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-04T10:34:55.795+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-04T10:34:55.798+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-04T10:34:55.800+05:30  INFO 17880 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-04T10:34:55.803+05:30  INFO 17880 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751605495802 with initial instances count: 3
2025-07-04T10:34:55.821+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-04T10:34:55.823+05:30  INFO 17880 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751605495823, current=UP, previous=STARTING]
2025-07-04T10:34:55.824+05:30  INFO 17880 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-04T10:34:55.826+05:30  WARN 17880 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-04T10:34:55.849+05:30  INFO 17880 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-04T10:34:55.850+05:30  INFO 17880 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-04T10:34:55.888+05:30  INFO 17880 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-04T10:34:55.891+05:30  INFO 17880 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 23.497 seconds (process running for 24.357)
2025-07-04T10:39:55.305+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T10:44:55.307+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T10:49:55.322+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T10:54:55.330+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T10:59:55.339+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:04:55.352+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:09:55.363+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:14:55.375+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:19:55.390+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:24:55.396+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:29:55.407+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:34:55.439+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T11:39:55.455+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:01:25.620+05:30  WARN 17880 --- [workplace-learning] [HikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Thread starvation or clock leap detected (housekeeper delta=18m46s427ms356µs200ns).
2025-07-04T12:03:16.573+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:08:16.580+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:13:16.583+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:18:16.597+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:23:16.603+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:28:16.606+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:33:16.623+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:38:16.635+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:43:16.648+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:48:16.668+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:53:16.673+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T12:58:16.687+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T13:03:16.699+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T13:08:17.836+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T13:13:17.842+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T13:18:17.858+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T13:23:17.870+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T13:28:17.886+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T14:30:49.723+05:30  WARN 17880 --- [workplace-learning] [HikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Thread starvation or clock leap detected (housekeeper delta=1h2m49s505ms553µs900ns).
2025-07-04T14:35:37.316+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T14:40:37.373+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T14:45:37.723+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T14:50:37.733+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T14:51:08.585+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04T14:51:08.587+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-04T14:51:08.609+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 20 ms
2025-07-04T14:51:09.034+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:09.035+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:09.056+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:09.097+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:09.099+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-25F5B7
2025-07-04T14:51:09.203+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 9f67e36e-0ae0-4d89-a41b-c95ff402b1c9
2025-07-04T14:51:09.366+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 9f67e36e-0ae0-4d89-a41b-c95ff402b1c9
2025-07-04T14:51:10.929+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 9f67e36e-0ae0-4d89-a41b-c95ff402b1c9
2025-07-04T14:51:14.802+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:14.802+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:14.803+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:14.806+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:14.806+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-D5CE11
2025-07-04T14:51:14.808+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: ea6bb436-7a0e-49ed-b6a1-c2248d545c9f
2025-07-04T14:51:14.830+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: ea6bb436-7a0e-49ed-b6a1-c2248d545c9f
2025-07-04T14:51:14.851+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: ea6bb436-7a0e-49ed-b6a1-c2248d545c9f
2025-07-04T14:51:22.887+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:22.889+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:22.892+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:22.897+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:22.898+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-2DA2F8
2025-07-04T14:51:22.903+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 752000ff-dbfc-40ad-a332-6589cb852049
2025-07-04T14:51:22.963+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 752000ff-dbfc-40ad-a332-6589cb852049
2025-07-04T14:51:23.012+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 752000ff-dbfc-40ad-a332-6589cb852049
2025-07-04T14:51:29.654+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:29.655+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:29.657+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:29.658+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:29.659+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-FA2518
2025-07-04T14:51:29.677+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: cedbd150-8046-4843-b8cb-556656c9f7e3
2025-07-04T14:51:29.722+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: cedbd150-8046-4843-b8cb-556656c9f7e3
2025-07-04T14:51:29.758+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: cedbd150-8046-4843-b8cb-556656c9f7e3
2025-07-04T14:51:36.128+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:36.129+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:36.130+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:36.131+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:36.132+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-94DFE5
2025-07-04T14:51:36.133+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 060978d0-aa60-49b8-9442-06c16d840455
2025-07-04T14:51:36.154+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 060978d0-aa60-49b8-9442-06c16d840455
2025-07-04T14:51:36.174+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 060978d0-aa60-49b8-9442-06c16d840455
2025-07-04T14:51:39.624+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-1] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@7a462992 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:51:39.654+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 9f67e36e-0ae0-4d89-a41b-c95ff402b1c9
2025-07-04T14:51:41.833+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:41.833+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:41.834+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:41.835+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:41.836+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-C6CED9
2025-07-04T14:51:41.838+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 453b2375-080d-44d5-98bf-82ad57af2ab3
2025-07-04T14:51:41.864+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 453b2375-080d-44d5-98bf-82ad57af2ab3
2025-07-04T14:51:41.882+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 453b2375-080d-44d5-98bf-82ad57af2ab3
2025-07-04T14:51:44.846+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-3] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@7a462992 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:51:44.928+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: ea6bb436-7a0e-49ed-b6a1-c2248d545c9f
2025-07-04T14:51:52.980+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-5] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@7a462992 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:51:53.118+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 752000ff-dbfc-40ad-a332-6589cb852049
2025-07-04T14:51:55.724+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:51:55.823+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:51:55.839+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:51:55.869+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:51:55.921+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-42089B
2025-07-04T14:51:55.954+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: f3ef9f30-55de-4a2a-ad0e-290f2b92d8e8
2025-07-04T14:51:56.229+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: f3ef9f30-55de-4a2a-ad0e-290f2b92d8e8
2025-07-04T14:51:56.543+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: f3ef9f30-55de-4a2a-ad0e-290f2b92d8e8
2025-07-04T14:51:59.744+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-7] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@7a462992 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:52:06.166+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-9] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@7a462992 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:52:09.683+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/9f67e36e-0ae0-4d89-a41b-c95ff402b1c9
2025-07-04T14:52:11.871+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-4] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@7a462992 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:52:14.872+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/ea6bb436-7a0e-49ed-b6a1-c2248d545c9f
2025-07-04T14:52:23.032+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-5] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/752000ff-dbfc-40ad-a332-6589cb852049
2025-07-04T14:52:24.343+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:52:24.343+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:52:24.344+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:52:24.346+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:52:24.346+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-DF9081
2025-07-04T14:52:24.348+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 10dc14f3-4991-4bfb-8330-07d67384865e
2025-07-04T14:52:24.372+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 10dc14f3-4991-4bfb-8330-07d67384865e
2025-07-04T14:52:26.269+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-2] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@1e8961d0 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:52:29.752+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/cedbd150-8046-4843-b8cb-556656c9f7e3
2025-07-04T14:52:30.817+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:52:30.817+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:52:30.819+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:52:30.820+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:52:30.821+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-ABA2D1
2025-07-04T14:52:30.823+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 59125245-8245-43b1-885a-ef85270a0f05
2025-07-04T14:52:30.853+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 59125245-8245-43b1-885a-ef85270a0f05
2025-07-04T14:52:36.176+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-9] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/060978d0-aa60-49b8-9442-06c16d840455
2025-07-04T14:52:40.306+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-07-04T14:52:40.308+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-07-04T14:52:40.315+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-07-04T14:52:40.321+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-07-04T14:52:40.323+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-C9BF63
2025-07-04T14:52:40.333+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 109ebbf4-9efe-49b6-b2a3-77acc600a9f0
2025-07-04T14:52:40.455+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 109ebbf4-9efe-49b6-b2a3-77acc600a9f0
2025-07-04T14:52:41.884+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/453b2375-080d-44d5-98bf-82ad57af2ab3
2025-07-04T14:52:54.378+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-8] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@fd28eb5 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:52:56.275+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/f3ef9f30-55de-4a2a-ad0e-290f2b92d8e8
2025-07-04T14:53:00.861+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-10] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@fd28eb5 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:53:04.468+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-07-04T14:53:04.481+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-04T14:53:05.240+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-04T14:53:05.244+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-04T14:53:05.424+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=9, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=9, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=9, ad9f2ba3-898c-487f-a40e-893a10fe8477=10}
2025-07-04T14:53:05.426+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 9dbc9c30-d927-47c3-b826-d46ddc42e2cd with 9 active complaints
2025-07-04T14:53:05.426+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-07-04T14:53:07.126+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 10dc14f3-4991-4bfb-8330-07d67384865e
2025-07-04T14:53:10.473+05:30  WARN 17880 --- [workplace-learning] [http-nio-8091-exec-6] .c.RetryAwareServiceInstanceListSupplier : No instances found after removing previously used service instance from the search ([EurekaServiceInstance@fd28eb5 instance = InstanceInfo [instanceId = DESKTOP-MIJ80IO.mshome.net:workflow:8082, appName = WORKFLOW, hostName = ************, status = UP, ipAddr = ************, port = 8082, securePort = 443, dataCenterInfo = com.netflix.appinfo.MyDataCenterInfo@5e3fd39b]). Returning all found instances.
2025-07-04T14:53:15.530+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 109ebbf4-9efe-49b6-b2a3-77acc600a9f0
2025-07-04T14:53:22.079+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 59125245-8245-43b1-885a-ef85270a0f05
2025-07-04T14:53:24.393+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/10dc14f3-4991-4bfb-8330-07d67384865e
2025-07-04T14:53:30.878+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-10] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/59125245-8245-43b1-885a-ef85270a0f05
2025-07-04T14:53:40.488+05:30 ERROR 17880 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: Read timed out executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/109ebbf4-9efe-49b6-b2a3-77acc600a9f0
2025-07-04T14:55:37.749+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:00:37.821+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:05:37.854+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:10:37.863+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:15:37.866+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:20:37.874+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:25:37.885+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:30:37.913+05:30  INFO 17880 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-04T15:32:04.778+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-07-04T15:32:04.781+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-04T15:32:06.734+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-04T15:32:06.739+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-04T15:32:06.833+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=9, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=9, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=10, ad9f2ba3-898c-487f-a40e-893a10fe8477=10}
2025-07-04T15:32:06.835+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 33bb3982-6234-401d-be81-ae20f1160b88 with 9 active complaints
2025-07-04T15:32:06.836+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 33bb3982-6234-401d-be81-ae20f1160b88
2025-07-04T15:32:16.438+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-07-04T15:32:16.438+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-04T15:32:16.976+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-04T15:32:16.976+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-04T15:32:16.990+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=10, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=9, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=10, ad9f2ba3-898c-487f-a40e-893a10fe8477=10}
2025-07-04T15:32:16.990+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent d1ee8398-e5e6-49e9-be1e-896b4e8cf289 with 9 active complaints
2025-07-04T15:32:16.991+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: d1ee8398-e5e6-49e9-be1e-896b4e8cf289
2025-07-04T15:32:23.017+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-07-04T15:32:23.018+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-04T15:32:23.570+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-04T15:32:23.571+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-04T15:32:23.585+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=10, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=10, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=10, ad9f2ba3-898c-487f-a40e-893a10fe8477=10}
2025-07-04T15:32:23.586+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent ad9f2ba3-898c-487f-a40e-893a10fe8477 with 10 active complaints
2025-07-04T15:32:23.588+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: ad9f2ba3-898c-487f-a40e-893a10fe8477
2025-07-04T15:33:23.297+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-07-04T15:33:23.298+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-04T15:33:23.883+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-04T15:33:23.886+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-04T15:33:23.924+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=10, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=10, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=10, ad9f2ba3-898c-487f-a40e-893a10fe8477=11}
2025-07-04T15:33:23.925+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 9dbc9c30-d927-47c3-b826-d46ddc42e2cd with 10 active complaints
2025-07-04T15:33:23.926+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-07-04T15:33:29.526+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-07-04T15:33:29.526+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-04T15:33:30.081+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-04T15:33:30.082+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-04T15:33:30.097+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=10, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=10, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=11, ad9f2ba3-898c-487f-a40e-893a10fe8477=11}
2025-07-04T15:33:30.100+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 33bb3982-6234-401d-be81-ae20f1160b88 with 10 active complaints
2025-07-04T15:33:30.101+05:30  INFO 17880 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 33bb3982-6234-401d-be81-ae20f1160b88
