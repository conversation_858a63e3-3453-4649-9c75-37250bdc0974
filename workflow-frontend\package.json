{"name": "workflow-frontend", "version": "1.0.0", "description": "Simple frontend for Flowable workflow management", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "serve -s dist -l 3000"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.4.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "serve": "^14.2.0"}, "keywords": ["flowable", "workflow", "react", "frontend"], "author": "", "license": "MIT"}