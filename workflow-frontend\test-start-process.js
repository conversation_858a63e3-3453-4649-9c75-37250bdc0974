const axios = require('axios');

async function testStartProcess() {
    try {
        console.log('Testing process start...');
        
        // Test 1: Start with minimal payload
        const payload1 = {
            processDefinitionKey: "oneTaskProcess"
        };
        
        console.log('Test 1 - Starting oneTaskProcess with minimal payload:', JSON.stringify(payload1, null, 2));
        
        const response1 = await axios.post(
            'http://localhost:8080/flowable-rest/service/runtime/process-instances',
            payload1,
            {
                headers: {
                    'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64'),
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ Test 1 SUCCESS:', response1.data);
        
    } catch (error) {
        console.error('❌ Test 1 FAILED:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
    }
    
    try {
        // Test 2: Start Complaint_Workflow with business key
        const payload2 = {
            processDefinitionKey: "Complaint_Workflow",
            businessKey: "TEST-123"
        };
        
        console.log('\nTest 2 - Starting Complaint_Workflow with business key:', JSON.stringify(payload2, null, 2));
        
        const response2 = await axios.post(
            'http://localhost:8080/flowable-rest/service/runtime/process-instances',
            payload2,
            {
                headers: {
                    'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64'),
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ Test 2 SUCCESS:', response2.data);
        
    } catch (error) {
        console.error('❌ Test 2 FAILED:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
    }
    
    try {
        // Test 3: Start with variables like your form
        const payload3 = {
            processDefinitionKey: "Complaint_Workflow",
            businessKey: "APP-2025-06-7212",
            variables: [
                {
                    name: "applicationNumber",
                    value: "APP-2025-06-7212"
                },
                {
                    name: "applicationType", 
                    value: "Complaint"
                }
            ]
        };
        
        console.log('\nTest 3 - Starting Complaint_Workflow with variables:', JSON.stringify(payload3, null, 2));
        
        const response3 = await axios.post(
            'http://localhost:8080/flowable-rest/service/runtime/process-instances',
            payload3,
            {
                headers: {
                    'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64'),
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ Test 3 SUCCESS:', response3.data);
        
    } catch (error) {
        console.error('❌ Test 3 FAILED:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

testStartProcess();
