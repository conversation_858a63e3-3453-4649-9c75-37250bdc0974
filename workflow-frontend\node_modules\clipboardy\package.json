{"name": "clipboardy", "version": "3.0.0", "description": "Access the system clipboard (copy/paste)", "license": "MIT", "repository": "sindresorhus/clipboardy", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"node": "./index.js", "default": "./browser.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "browser.js", "lib", "fallbacks"], "keywords": ["clipboard", "copy", "paste", "copy-paste", "pasteboard", "read", "write", "pbcopy", "clip", "xclip", "xsel"], "dependencies": {"arch": "^2.2.0", "execa": "^5.1.1", "is-wsl": "^2.2.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}, "ava": {"serial": true}}