const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function deployProcess() {
    try {
        const form = new FormData();
        
        // Add the BPMN file
        const bpmnPath = path.join(__dirname, 'simple-process.bpmn20.xml');
        form.append('deployment', fs.createReadStream(bpmnPath), {
            filename: 'simple-process.bpmn20.xml',
            contentType: 'application/xml'
        });
        
        // Add deployment name
        form.append('deploymentName', 'Simple Test Process Deployment');
        
        const response = await axios.post(
            'http://localhost:8080/flowable-rest/service/repository/deployments',
            form,
            {
                headers: {
                    ...form.getHeaders(),
                    'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64')
                }
            }
        );
        
        console.log('✅ Process deployed successfully!');
        console.log('Deployment ID:', response.data.id);
        console.log('Deployment Name:', response.data.name);
        console.log('Deployment Time:', response.data.deploymentTime);
        
        // List process definitions to confirm
        const defsResponse = await axios.get(
            'http://localhost:8080/flowable-rest/service/repository/process-definitions',
            {
                headers: {
                    'Authorization': 'Basic ' + Buffer.from('rest-admin:test').toString('base64')
                }
            }
        );
        
        console.log('\n📋 Available Process Definitions:');
        defsResponse.data.data.forEach(def => {
            console.log(`- ${def.name} (${def.key}) v${def.version}`);
        });
        
    } catch (error) {
        console.error('❌ Error deploying process:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

deployProcess();
