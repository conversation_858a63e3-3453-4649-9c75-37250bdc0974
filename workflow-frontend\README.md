# Flowable Process Instance Manager

A simple frontend interface to interact with Flowable REST API for managing process instances. This tool allows you to view, suspend, activate, delete, and start new process instances.

## Features

- **View Process Instances**: List all running and completed process instances
- **Update Process Instances**: Suspend or activate running processes
- **Delete Process Instances**: Remove process instances with confirmation
- **Start New Processes**: Create new process instances with custom variables
- **Real-time Status**: See current status (Active, Suspended, Ended)
- **Process Details**: View detailed information about each process instance

## Available Versions

### 1. Standalone HTML Version (Recommended for Quick Start)

**File**: `index.html`

This is a simple HTML file that can be opened directly in any web browser. It uses:
- Tailwind CSS (via CDN)
- Axios (via CDN)
- Vanilla JavaScript

**To use:**
1. Simply open `index.html` in your web browser
2. No installation or build process required
3. Works immediately with your Flowable instance

### 2. React TypeScript Version

**Files**: React app in `src/` directory

This is a full React application with TypeScript support.

**To use:**
1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Open http://localhost:3001 in your browser

## Configuration

### Flowable REST API Settings

Both versions are configured to connect to:
- **URL**: `http://localhost:8080/flowable-rest`
- **Authentication**: Basic Auth with `rest-admin:test`

### Updating Configuration

**For HTML version**: Edit the JavaScript variables in `index.html`:
```javascript
const FLOWABLE_BASE_URL = 'http://localhost:8080/flowable-rest';
const AUTH_HEADER = 'Basic ' + btoa('rest-admin:test');
```

**For React version**: Edit the constants in `src/components/ProcessInstanceManager.tsx`:
```typescript
const FLOWABLE_BASE_URL = 'http://localhost:8080/flowable-rest';
const AUTH_HEADER = {
  Authorization: 'Basic ' + btoa('rest-admin:test')
};
```

## API Endpoints Used

This application interacts with the following Flowable REST API endpoints:

1. **GET** `/service/runtime/process-instances` - List all process instances
2. **PUT** `/service/runtime/process-instances/{id}` - Update process instance (suspend/activate)
3. **DELETE** `/service/runtime/process-instances/{id}` - Delete process instance
4. **POST** `/service/runtime/process-instances` - Start new process instance
5. **GET** `/service/runtime/process-instances/{id}/variables` - Get process variables

## Process Types Supported

The application is configured to work with your existing process definitions:
- **Hrdc_workflow** - Main HRDC workflow
- **Complaint_Workflow** - Complaint handling workflow  
- **Appeal_Workflow** - Appeal processing workflow

## Usage Instructions

### Viewing Process Instances
1. Click "Refresh List" to load current process instances
2. View details including ID, process key, business key, status, and start time

### Managing Process Instances
- **Suspend**: Click "Suspend" to pause a running process
- **Activate**: Click "Activate" to resume a suspended process  
- **Delete**: Click "Delete" to permanently remove a process (with confirmation)

### Starting New Processes
1. Click "Start New Process"
2. Select the process definition key
3. Optionally enter a business key
4. Enter application number and type
5. Click "Start Process"

## Integration with Your Existing System

### Option 1: Standalone Usage
Use the HTML version as a separate admin tool for workflow management.

### Option 2: Integration into Existing React App
Copy the `ProcessInstanceManager.tsx` component into your existing React application:

```typescript
import ProcessInstanceManager from './components/ProcessInstanceManager';

function AdminDashboard() {
  return (
    <div>
      <h1>Admin Dashboard</h1>
      <ProcessInstanceManager />
    </div>
  );
}
```

### Option 3: Embed in Existing Frontend
Add the HTML version as an iframe or integrate the JavaScript functions into your existing admin panel.

## Troubleshooting

### CORS Issues
If you encounter CORS errors, you may need to:
1. Configure CORS in your Flowable application
2. Use the React version with proxy configuration (already included in `vite.config.ts`)

### Authentication Issues
- Verify Flowable REST API is enabled in your `application.yml`
- Check that the authentication credentials are correct
- Ensure the Flowable REST endpoint is accessible

### Connection Issues
- Verify Flowable is running on `http://localhost:8080`
- Check that the REST API is enabled with `flowable.rest-api.enabled: true`
- Test the API directly: `curl -u rest-admin:test http://localhost:8080/flowable-rest/service/runtime/process-instances`

## Development

### React Version Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Customization
- Modify the UI styling by editing Tailwind classes
- Add new process definition keys in the start process modal
- Extend functionality by adding more Flowable API endpoints
- Customize authentication by modifying the AUTH_HEADER configuration

## Security Notes

- The default credentials (`rest-admin:test`) should be changed in production
- Consider implementing proper authentication and authorization
- Use HTTPS in production environments
- Validate user permissions before allowing process management operations
